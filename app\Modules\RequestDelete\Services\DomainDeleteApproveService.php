<?php

namespace App\Modules\RequestDelete\Services;

use App\Mail\UserDeleteRequestMail;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;

class DomainDeleteApproveService
{
    private Carbon $now;

    public function __construct()
    {
        $this->now = Carbon::now();
    }

    public static function instance()
    {
        return new self;
    }

    public function approveDeleteRequestSave($requestOrData)
    {
        $data = is_array($requestOrData) ? $requestOrData : $requestOrData->all();
        $this->processApproveRequest($data);
    }

    private function processApproveRequest(array $data): void
    {
        $adminId = Auth::id();
        $adminName = Auth::user()->name ?? 'System';
        $adminEmail = Auth::user()->email ?? '<EMAIL>';
        $supportNote = "Request delete approved by {$adminEmail}";

        // Only mark as approved in database - deletion will be handled by cron job
        $this->markAsApproved($data, $adminId, $adminName, $supportNote);

        // Send notifications about approval and processing time
        $this->userNotification($data);
        $this->userEmailNotification($data);
    }

    private function userNotification($requestData)
    {
        $userID = $requestData['userID'];
        $domainName = $requestData['domainName'];

        if (!$userID || !$domainName) return;

        $message = 'Your request to delete the domain "' . $domainName . '" has been approved. The deletion process will take 1-2 days to complete.';

        DB::client()->table('notifications')->insert([
            'user_id'      => $userID,
            'title'        => 'Domain Deletion Request Approved',
            'message'      => $message,
            'redirect_url' => '/domain',
            'created_at'   => now(),
            'importance'   => 'important',
        ]);
    }

    private function userEmailNotification($requestData)
    {
        $domainName = $requestData['domainName'];
        $userEmail = $requestData['userEmail'];

        $body = 'Your request to delete the domain "' . $domainName . '" has been approved. The deletion process will take 1-2 days to complete. This action is final and cannot be undone.';

        $message = [
            'subject'  => 'Domain Deletion Request Approved',
            'greeting' => 'Greetings!',
            'body'     => $body,
            'text'     => Carbon::now()->format('Y-m-d H:i:s'),
            'sender'   => 'StrangeDomains Support',
        ];

        Mail::to($userEmail)->send(new UserDeleteRequestMail($message));

        $this->emailTrack($userEmail, $message, $requestData['domainId']);
    }

    private function getUserByDomain($domainId) 
    {
        return DB::client()->table('registered_domains')
            ->select('users.id as user_id', 'users.email', 'users.first_name', 'users.last_name')
            ->join('user_contacts', 'user_contacts.id', '=', 'registered_domains.user_contact_registrar_id')
            ->join('users', 'users.id', '=', 'user_contacts.user_id')
            ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->where('domains.id', $domainId)
            ->first();
    }

    private function emailTrack($email, array $payload, $domainId = null) 
    {
        $userId = null;
        $userName = null;

        if ($domainId) {
            $user = $this->getUserByDomain($domainId);
            if ($user) {
                $userId = $user->user_id;
                $userName = $user->first_name . ' ' . $user->last_name;
            }
        }

        $emailSent = DB::client()->table('email_histories')
            ->insert([
                'user_id' => $userId,
                'name' => $userName ?? 'System',
                'recipient_email' => $email,
                'subject' => 'Domain Deletion Request Approved',
                'email_type' => 'Domain Deletion Request Approved',
                'email_body' => json_encode($payload),
                'attachment' => null,
                'created_at' => now(),
                'updated_at' => now()
            ]);

        return $emailSent;
    }

    private function markAsApproved(array $data, int $adminId, string $adminName, string $supportNote): void
    {
        // Only update the domain cancellation request to mark as approved
        // EPP deletion and local database updates will be handled by cron job
        $this->updateDomainCancellationRequest($data, $adminId, $adminName, $supportNote);
    }

    private function updateDomainCancellationRequest(array $data, int $adminId, string $adminName, string $supportNote): void
    {
        $exists = DB::client()
            ->table('domain_cancellation_requests')
            ->where('domain_id', $data['domainId'])
            ->exists();

        if (!$exists) {
            return;
        }

        $date = Carbon::parse($data['createdDate'] ?? now());
        $is_refunded = $date->greaterThanOrEqualTo(now()->subDays(5)) ? false : true;

        $adminEmail = Auth::user()->email ?? '<EMAIL>';
        $adminFullName = "{$adminName} ({$adminEmail})";

        DB::client()->table('domain_cancellation_requests')
            ->where('domain_id', $data['domainId'])
            ->update([
                'support_agent_id'   => $adminId,
                'support_agent_name' => $adminFullName,
                'feedback_date'      => now(),
                'support_note'       => $supportNote,
                'is_refunded'        => $is_refunded,
                // deleted_at will be set by cron job when EPP deletion is processed
            ]);
    }
}
