<?php

namespace App\Modules\RequestDelete\Services;

use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DomainDeleteService
{
    private Carbon $now;

    public function __construct()
    {
        $this->now = Carbon::now();
    }

    public static function instance()
    {
        return new self;
    }

    public function approveDeleteRequestSave($requestOrData)
    {
        return DomainDeleteApproveService::instance()->approveDeleteRequestSave($requestOrData);
    }

    public function rejectDeleteRequestSave($request)
    {
        return DomainDeleteRejectService::instance()->rejectDeleteRequestSave($request);
    }

    public function createDeleteRequestSave($request)
    {
        return DomainDeleteCreateService::instance()->createDeleteRequestSave($request);
    }

    public function processExpiredRequests()
    {
        $expiredRequests = DB::client()->table('domain_cancellation_requests')
            ->join('domains', 'domain_cancellation_requests.domain_id', '=', 'domains.id')
            ->join('users', 'domain_cancellation_requests.user_id', '=', 'users.id')
            ->select([
                'domain_cancellation_requests.domain_id as domainId',
                'domains.name as domainName',
                'domain_cancellation_requests.user_id as userID',
                'users.email as userEmail',
                'domain_cancellation_requests.reason',
                'domain_cancellation_requests.requested_at as createdDate'
            ])
            ->where('domains.status', 'IN_PROCESS')
            ->whereNull('domain_cancellation_requests.support_agent_id')
            ->where('domain_cancellation_requests.requested_at', '<=', now()->subHours(24))
            ->get();

        foreach ($expiredRequests as $request) {
            $data = [
                'domainId' => $request->domainId,
                'domainName' => $request->domainName,
                'userID' => $request->userID,
                'userEmail' => $request->userEmail,
                'reason' => $request->reason,
                'createdDate' => $request->createdDate,
                'is_scheduler' => true,
            ];

            $this->approveDeleteRequestSave($data);
        }

        return count($expiredRequests);
    }
}
