<?php

namespace App\Modules\RequestDelete\Services;

use App\Modules\RequestDelete\Jobs\DomainEppCancellation;
use App\Modules\CustomLogger\Services\AuthLogger;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DomainDeleteService
{
    private Carbon $now;

    public function __construct()
    {
        $this->now = Carbon::now();
    }

    public static function instance()
    {
        return new self;
    }

    public function approveDeleteRequestSave($requestOrData)
    {
        return DomainDeleteApproveService::instance()->approveDeleteRequestSave($requestOrData);
    }

    public function rejectDeleteRequestSave($request)
    {
        return DomainDeleteRejectService::instance()->rejectDeleteRequestSave($request);
    }

    public function createDeleteRequestSave($request)
    {
        return DomainDeleteCreateService::instance()->createDeleteRequestSave($request);
    }

    public function processExpiredRequests()
    {
        // Get approved requests that haven't been processed yet
        $approvedRequests = DB::client()->table('domain_cancellation_requests')
            ->join('domains', 'domain_cancellation_requests.domain_id', '=', 'domains.id')
            ->join('users', 'domain_cancellation_requests.user_id', '=', 'users.id')
            ->select([
                'domain_cancellation_requests.domain_id as domainId',
                'domains.name as domainName',
                'domain_cancellation_requests.user_id as userID',
                'users.email as userEmail',
                'domain_cancellation_requests.reason',
                'domain_cancellation_requests.feedback_date as approvedDate',
                'domain_cancellation_requests.support_agent_id',
                'domain_cancellation_requests.support_agent_name'
            ])
            ->where('domains.status', 'IN_PROCESS')
            ->whereNotNull('domain_cancellation_requests.support_agent_id')
            ->whereNull('domain_cancellation_requests.deleted_at')
            // ->where('domain_cancellation_requests.feedback_date', '<=', now()->subHours(24))
            ->get();

        foreach ($approvedRequests as $request) {
            $data = [
                'domainId' => $request->domainId,
                'domainName' => $request->domainName,
                'userId' => $request->userID,
                'userEmail' => $request->userEmail,
                'reason' => $request->reason,
                'createdDate' => $request->approvedDate,
                'adminId' => $request->support_agent_id,
                'adminName' => 'System',
                'adminEmail' => '<EMAIL>',
                'supportNote' => "Deletion processed automatically after 24+ hours"
            ];

            $this->processApprovedRequest($data);
        }

        return count($approvedRequests);
    }

    private function processApprovedRequest(array $data): void
    {
        // Dispatch EPP job to handle both EPP deletion and local database updates
        DomainEppCancellation::dispatch(
            $data['domainId'],
            $data['domainName'],
            $data['userId'],
            $data['userEmail'],
            $data['reason'] ?? 'Domain deletion request',
            $data['createdDate'] ?? now()->toDateTimeString(),
            $data['supportNote'],
            $data['adminId'],
            $data['adminName'],
            $data['adminEmail']
        );
    }
}
