<?php

namespace App\Modules\RequestDelete\Services;

use App\Mail\UserDeleteRequestMail;
use App\Modules\RequestDelete\Jobs\DomainEppCancellation;
use App\Util\Constant\UserDomainStatus;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;

class DomainDeleteCreateService
{
    private Carbon $now;

    public function __construct()
    {
        $this->now = Carbon::now();
    }

    public static function instance()
    {
        return new self;
    }

    public function createDeleteRequestSave($request)
    {
        $data = $request->all();
        $this->processCreateRequest($data);
    }

    private function processCreateRequest(array $data): void
    {
        $adminId = Auth::id();
        $adminName = Auth::user()->name ?? 'System';
        $adminEmail = Auth::user()->email ?? '<EMAIL>';
        $supportNote = "Request delete created by {$adminEmail}";

        $this->localDelete($data, $adminId, $adminName, $supportNote);

        DomainEppCancellation::dispatch(
            $data['domainId'],
            $data['domainName'],
            $data['userID'],
            $data['userEmail'],
            $data['reason'] ?? 'Domain deletion request',
            $data['createdDate'] ?? now()->toDateTimeString(),
            $supportNote,
            $adminId,
            $adminName,
            $adminEmail
        );

        $this->userNotification($data);
        $this->userEmailNotification($data);
    }

    private function userNotification($requestData)
    {
        $userID = $requestData['userID'];
        $domainName = $requestData['domainName'];

        if (!$userID || !$domainName) return;

        $message = 'Your request to delete the domain "' . $domainName . '" has been created and is being processed.';

        DB::client()->table('notifications')->insert([
            'user_id'      => $userID,
            'title'        => 'Domain Deletion Request Created',
            'message'      => $message,
            'redirect_url' => '/domain',
            'created_at'   => now(),
            'importance'   => 'important',
        ]);
    }

    private function userEmailNotification($requestData)
    {
        $domainName = $requestData['domainName'];
        $userEmail = $requestData['userEmail'];

        $body = 'Your request to delete the domain "' . $domainName . '" has been created. The domain will be removed from your account and queued for deletion shortly. This action is final and cannot be undone.';

        $message = [
            'subject'  => 'Domain Deletion Request Created',
            'greeting' => 'Greetings!',
            'body'     => $body,
            'text'     => Carbon::now()->format('Y-m-d H:i:s'),
            'sender'   => 'StrangeDomains Support',
        ];

        Mail::to($userEmail)->send(new UserDeleteRequestMail($message));

        $this->emailTrack($userEmail, $message, $requestData['domainId']);
    }

    private function getUserByDomain($domainId) 
    {
        return DB::client()->table('registered_domains')
            ->select('users.id as user_id', 'users.email', 'users.first_name', 'users.last_name')
            ->join('user_contacts', 'user_contacts.id', '=', 'registered_domains.user_contact_registrar_id')
            ->join('users', 'users.id', '=', 'user_contacts.user_id')
            ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->where('domains.id', $domainId)
            ->first();
    }

    private function emailTrack($email, array $payload, $domainId = null) 
    {
        $userId = null;
        $userName = null;

        if ($domainId) {
            $user = $this->getUserByDomain($domainId);
            if ($user) {
                $userId = $user->user_id;
                $userName = $user->first_name . ' ' . $user->last_name;
            }
        }

        $emailSent = DB::client()->table('email_histories')
            ->insert([
                'user_id' => $userId,
                'name' => $userName ?? 'System',
                'recipient_email' => $email,
                'subject' => 'Domain Deletion Request Created',
                'email_type' => 'Domain Deletion Request Created',
                'email_body' => json_encode($payload),
                'attachment' => null,
                'created_at' => now(),
                'updated_at' => now()
            ]);

        return $emailSent;
    }

    private function localDelete(array $data, int $adminId, string $adminName, string $supportNote): void
    {
        $timestamp = now();

        $updates = [
            'status'     => UserDomainStatus::DELETED,
            'deleted_at' => $timestamp,
            'updated_at' => $timestamp,
        ];

        DB::client()->table('registered_domains')
            ->where('domain_id', $data['domainId'])
            ->update($updates);

        DB::client()->table('domains')
            ->where('id', $data['domainId'])
            ->update($updates);

        $this->updateDomainCancellationRequest($data, $adminId, $adminName, $supportNote);

        DB::client()->table('pending_domain_deletions')->insert([
            'registered_domain_id' => $data['domainId'],
            'deleted_by' => $data['userEmail'],
            'deleted_at' => $timestamp,
            'created_at' => $timestamp,
            'updated_at' => $timestamp,
        ]);
    }

    private function updateDomainCancellationRequest(array $data, int $adminId, string $adminName, string $supportNote): void
    {
        $exists = DB::client()
            ->table('domain_cancellation_requests')
            ->where('domain_id', $data['domainId'])
            ->exists();

        if (!$exists) {
            return;
        }

        $date = Carbon::parse($data['createdDate'] ?? now());
        $is_refunded = $date->greaterThanOrEqualTo(now()->subDays(5)) ? false : true;

        $adminEmail = Auth::user()->email ?? '<EMAIL>';
        $adminFullName = "{$adminName} ({$adminEmail})";

        DB::client()->table('domain_cancellation_requests')
            ->where('domain_id', $data['domainId'])
            ->update([
                'support_agent_id'   => $adminId,
                'support_agent_name' => $adminFullName,
                'deleted_at'         => now(),
                'feedback_date'      => now(),
                'support_note'       => $supportNote,
                'is_refunded'        => $is_refunded,
            ]);
    }
}
