[2025-08-27 01:49:02] local.INFO: ApprovalDeleteRequest: Running...  
[2025-08-27 01:49:02] local.INFO: ApprovalDeleteRequest: Processed 0 expired requests  
[2025-08-27 01:49:02] local.INFO: ApprovalDeleteRequest: Done  
[2025-08-27 01:50:01] local.INFO: ApprovalDeleteRequest: Running...  
[2025-08-27 01:50:01] local.INFO: ApprovalDeleteRequest: Processed 0 expired requests  
[2025-08-27 01:50:01] local.INFO: ApprovalDeleteRequest: Done  
[2025-08-27 01:52:01] local.INFO: ApprovalDeleteRequest: Running...  
[2025-08-27 01:52:01] local.INFO: ApprovalDeleteRequest: Processed 0 expired requests  
[2025-08-27 01:52:01] local.INFO: ApprovalDeleteRequest: Done  
[2025-08-27 01:56:36] local.INFO: ApprovalDeleteRequest: Running...  
[2025-08-27 01:56:37] local.INFO: ApprovalDeleteRequest: All requests in database: [{"id":3,"domain_id":5,"domainName":"teachmeone.net","domainStatus":"PENDING","support_agent_id":null,"deleted_at":"2025-08-18 15:23:20","feedback_date":null},{"id":6,"domain_id":6,"domainName":"porqueto.com","domainStatus":"DELETED","support_agent_id":null,"deleted_at":"2025-08-19 02:15:54","feedback_date":"2025-08-19 02:15:54"},{"id":1,"domain_id":3,"domainName":"handlerme.org","domainStatus":"DELETED","support_agent_id":1,"deleted_at":"2025-08-18 07:17:06","feedback_date":"2025-08-18 07:17:06"},{"id":2,"domain_id":1,"domainName":"handlerme.com","domainStatus":"DELETED","support_agent_id":1,"deleted_at":"2025-08-18 07:18:06","feedback_date":"2025-08-18 07:18:06"},{"id":4,"domain_id":2,"domainName":"handlerme.net","domainStatus":"PENDING","support_agent_id":1,"deleted_at":"2025-08-19 01:11:12","feedback_date":"2025-08-19 01:11:12"},{"id":5,"domain_id":2,"domainName":"handlerme.net","domainStatus":"PENDING","support_agent_id":1,"deleted_at":"2025-08-19 01:18:31","feedback_date":"2025-08-19 01:18:31"},{"id":7,"domain_id":9,"domainName":"poktare.com","domainStatus":"DELETED","support_agent_id":1,"deleted_at":"2025-08-19 05:19:58","feedback_date":"2025-08-19 05:19:58"},{"id":8,"domain_id":10,"domainName":"hajasawa.com","domainStatus":"DELETED","support_agent_id":1,"deleted_at":"2025-08-19 05:30:12","feedback_date":"2025-08-19 05:30:12"},{"id":12,"domain_id":114,"domainName":"onalaskaw.com","domainStatus":"DELETED","support_agent_id":1,"deleted_at":"2025-08-26 01:50:31","feedback_date":"2025-08-26 01:50:31"},{"id":13,"domain_id":115,"domainName":"onalaskaw.net","domainStatus":"DELETED","support_agent_id":1,"deleted_at":"2025-08-26 01:50:35","feedback_date":"2025-08-26 01:50:35"},{"id":14,"domain_id":116,"domainName":"onalaskaw.org","domainStatus":"DELETED","support_agent_id":1,"deleted_at":"2025-08-26 01:50:46","feedback_date":"2025-08-26 01:50:46"},{"id":16,"domain_id":118,"domainName":"lakasxzasd.net","domainStatus":"DELETED","support_agent_id":1,"deleted_at":"2025-08-26 07:05:06","feedback_date":"2025-08-26 07:05:06"},{"id":15,"domain_id":119,"domainName":"lakasxzasd.org","domainStatus":"IN_PROCESS","support_agent_id":1,"deleted_at":"2025-08-26 15:02:41","feedback_date":"2025-08-26 07:18:42"},{"id":9,"domain_id":111,"domainName":"limittestme.com","domainStatus":"IN_PROCESS","support_agent_id":1,"deleted_at":"2025-08-22 16:34:09","feedback_date":"2025-08-26 07:20:53"},{"id":10,"domain_id":112,"domainName":"limittestme.net","domainStatus":"IN_PROCESS","support_agent_id":1,"deleted_at":"2025-08-22 16:43:05","feedback_date":"2025-08-26 07:22:01"},{"id":11,"domain_id":113,"domainName":"limittestme.org","domainStatus":"ACTIVE","support_agent_id":1,"deleted_at":"2025-08-22 16:46:02","feedback_date":"2025-08-26 07:46:19"},{"id":17,"domain_id":131,"domainName":"palwataw.org","domainStatus":"IN_PROCESS","support_agent_id":null,"deleted_at":"2025-08-27 09:17:43","feedback_date":null},{"id":18,"domain_id":130,"domainName":"palwataw.net","domainStatus":"IN_PROCESS","support_agent_id":null,"deleted_at":"2025-08-27 09:17:55","feedback_date":null},{"id":22,"domain_id":122,"domainName":"intrusivetot.org","domainStatus":"DELETED","support_agent_id":1,"deleted_at":"2025-08-27 01:20:19","feedback_date":"2025-08-27 01:20:19"},{"id":21,"domain_id":120,"domainName":"intrusivetot.com","domainStatus":"ACTIVE","support_agent_id":1,"deleted_at":"2025-08-27 09:18:36","feedback_date":"2025-08-27 01:21:06"},{"id":20,"domain_id":124,"domainName":"intersive.net","domainStatus":"DELETED","support_agent_id":1,"deleted_at":"2025-08-27 01:21:36","feedback_date":"2025-08-27 01:21:36"},{"id":23,"domain_id":121,"domainName":"intrusivetot.net","domainStatus":"DELETED","support_agent_id":1,"deleted_at":"2025-08-27 01:24:52","feedback_date":"2025-08-27 01:24:52"},{"id":19,"domain_id":127,"domainName":"colacowa.net","domainStatus":"IN_PROCESS","support_agent_id":1,"deleted_at":"2025-08-27 09:18:06","feedback_date":"2025-08-27 01:46:51"}]  
[2025-08-27 01:56:37] local.INFO: ApprovalDeleteRequest: Found approved requests: []  
[2025-08-27 01:56:37] local.INFO: ApprovalDeleteRequest: Processed 0 expired requests  
[2025-08-27 01:56:37] local.INFO: ApprovalDeleteRequest: Done  
[2025-08-27 02:01:30] local.INFO: ApprovalDeleteRequest: Running...  
[2025-08-27 02:01:30] local.INFO: ApprovalDeleteRequest: All requests in database: [{"id":3,"domain_id":5,"domainName":"teachmeone.net","domainStatus":"PENDING","support_agent_id":null,"deleted_at":"2025-08-18 15:23:20","feedback_date":null},{"id":6,"domain_id":6,"domainName":"porqueto.com","domainStatus":"DELETED","support_agent_id":null,"deleted_at":"2025-08-19 02:15:54","feedback_date":"2025-08-19 02:15:54"},{"id":1,"domain_id":3,"domainName":"handlerme.org","domainStatus":"DELETED","support_agent_id":1,"deleted_at":"2025-08-18 07:17:06","feedback_date":"2025-08-18 07:17:06"},{"id":2,"domain_id":1,"domainName":"handlerme.com","domainStatus":"DELETED","support_agent_id":1,"deleted_at":"2025-08-18 07:18:06","feedback_date":"2025-08-18 07:18:06"},{"id":4,"domain_id":2,"domainName":"handlerme.net","domainStatus":"PENDING","support_agent_id":1,"deleted_at":"2025-08-19 01:11:12","feedback_date":"2025-08-19 01:11:12"},{"id":5,"domain_id":2,"domainName":"handlerme.net","domainStatus":"PENDING","support_agent_id":1,"deleted_at":"2025-08-19 01:18:31","feedback_date":"2025-08-19 01:18:31"},{"id":7,"domain_id":9,"domainName":"poktare.com","domainStatus":"DELETED","support_agent_id":1,"deleted_at":"2025-08-19 05:19:58","feedback_date":"2025-08-19 05:19:58"},{"id":8,"domain_id":10,"domainName":"hajasawa.com","domainStatus":"DELETED","support_agent_id":1,"deleted_at":"2025-08-19 05:30:12","feedback_date":"2025-08-19 05:30:12"},{"id":12,"domain_id":114,"domainName":"onalaskaw.com","domainStatus":"DELETED","support_agent_id":1,"deleted_at":"2025-08-26 01:50:31","feedback_date":"2025-08-26 01:50:31"},{"id":13,"domain_id":115,"domainName":"onalaskaw.net","domainStatus":"DELETED","support_agent_id":1,"deleted_at":"2025-08-26 01:50:35","feedback_date":"2025-08-26 01:50:35"},{"id":14,"domain_id":116,"domainName":"onalaskaw.org","domainStatus":"DELETED","support_agent_id":1,"deleted_at":"2025-08-26 01:50:46","feedback_date":"2025-08-26 01:50:46"},{"id":16,"domain_id":118,"domainName":"lakasxzasd.net","domainStatus":"DELETED","support_agent_id":1,"deleted_at":"2025-08-26 07:05:06","feedback_date":"2025-08-26 07:05:06"},{"id":15,"domain_id":119,"domainName":"lakasxzasd.org","domainStatus":"IN_PROCESS","support_agent_id":1,"deleted_at":"2025-08-26 15:02:41","feedback_date":"2025-08-26 07:18:42"},{"id":9,"domain_id":111,"domainName":"limittestme.com","domainStatus":"IN_PROCESS","support_agent_id":1,"deleted_at":"2025-08-22 16:34:09","feedback_date":"2025-08-26 07:20:53"},{"id":10,"domain_id":112,"domainName":"limittestme.net","domainStatus":"IN_PROCESS","support_agent_id":1,"deleted_at":"2025-08-22 16:43:05","feedback_date":"2025-08-26 07:22:01"},{"id":11,"domain_id":113,"domainName":"limittestme.org","domainStatus":"ACTIVE","support_agent_id":1,"deleted_at":"2025-08-22 16:46:02","feedback_date":"2025-08-26 07:46:19"},{"id":17,"domain_id":131,"domainName":"palwataw.org","domainStatus":"IN_PROCESS","support_agent_id":null,"deleted_at":"2025-08-27 09:17:43","feedback_date":null},{"id":18,"domain_id":130,"domainName":"palwataw.net","domainStatus":"IN_PROCESS","support_agent_id":null,"deleted_at":"2025-08-27 09:17:55","feedback_date":null},{"id":22,"domain_id":122,"domainName":"intrusivetot.org","domainStatus":"DELETED","support_agent_id":1,"deleted_at":"2025-08-27 01:20:19","feedback_date":"2025-08-27 01:20:19"},{"id":21,"domain_id":120,"domainName":"intrusivetot.com","domainStatus":"ACTIVE","support_agent_id":1,"deleted_at":"2025-08-27 09:18:36","feedback_date":"2025-08-27 01:21:06"},{"id":20,"domain_id":124,"domainName":"intersive.net","domainStatus":"DELETED","support_agent_id":1,"deleted_at":"2025-08-27 01:21:36","feedback_date":"2025-08-27 01:21:36"},{"id":23,"domain_id":121,"domainName":"intrusivetot.net","domainStatus":"DELETED","support_agent_id":1,"deleted_at":"2025-08-27 01:24:52","feedback_date":"2025-08-27 01:24:52"},{"id":19,"domain_id":127,"domainName":"colacowa.net","domainStatus":"IN_PROCESS","support_agent_id":1,"deleted_at":"2025-08-27 09:18:06","feedback_date":"2025-08-27 01:46:51"}]  
[2025-08-27 02:01:30] local.INFO: ApprovalDeleteRequest: Found approved requests: []  
[2025-08-27 02:01:30] local.INFO: ApprovalDeleteRequest: Processed 0 expired requests  
[2025-08-27 02:01:30] local.INFO: ApprovalDeleteRequest: Done  
[2025-08-27 02:17:54] local.ERROR: {"query":[],"parameter":{"domainName":"palwataw.net","userEmail":"<EMAIL>","domainId":130,"createdDate":"2025-08-27 01:16:37","userID":7,"email":"<EMAIL>"},"error":"Illuminate\\Database\\QueryException","message":"SQLSTATE[23502]: Not null violation: 7 ERROR:  null value in column \"deleted_at\" of relation \"domain_cancellation_requests\" violates not-null constraint
DETAIL:  Failing row contains (18, 7, 130, Security\/Privacy Concerns, 2025-08-27 01:17:54, null, 1, admin 1 (a@a.a), Request delete approved by a@a.a, 2025-08-27 02:17:54, f, null). (Connection: client, SQL: update \"domain_cancellation_requests\" set \"support_agent_id\" = 1, \"support_agent_name\" = admin 1 (a@a.a), \"feedback_date\" = 2025-08-27 02:17:54, \"support_note\" = Request delete approved by a@a.a, \"is_refunded\" = 0, \"deleted_at\" = ? where \"domain_id\" = 130)","code":"23502"}  
[2025-08-27 02:17:58] local.ERROR: {"query":[],"parameter":{"domainName":"palwataw.net","userEmail":"<EMAIL>","domainId":130,"createdDate":"2025-08-27 01:16:37","userID":7,"email":"<EMAIL>"},"error":"Illuminate\\Database\\QueryException","message":"SQLSTATE[23502]: Not null violation: 7 ERROR:  null value in column \"deleted_at\" of relation \"domain_cancellation_requests\" violates not-null constraint
DETAIL:  Failing row contains (18, 7, 130, Security\/Privacy Concerns, 2025-08-27 01:17:54, null, 1, admin 1 (a@a.a), Request delete approved by a@a.a, 2025-08-27 02:17:58, f, null). (Connection: client, SQL: update \"domain_cancellation_requests\" set \"support_agent_id\" = 1, \"support_agent_name\" = admin 1 (a@a.a), \"feedback_date\" = 2025-08-27 02:17:58, \"support_note\" = Request delete approved by a@a.a, \"is_refunded\" = 0, \"deleted_at\" = ? where \"domain_id\" = 130)","code":"23502"}  
[2025-08-27 02:18:02] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-27 02:24:54] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-27 02:25:06] local.ERROR: {"query":[],"parameter":{"domainName":"palwataw.net","userEmail":"<EMAIL>","domainId":130,"createdDate":"2025-08-27 01:16:37","userID":7,"email":"<EMAIL>"},"error":"Illuminate\\Database\\QueryException","message":"SQLSTATE[23502]: Not null violation: 7 ERROR:  null value in column \"deleted_at\" of relation \"domain_cancellation_requests\" violates not-null constraint
DETAIL:  Failing row contains (18, 7, 130, Security\/Privacy Concerns, 2025-08-27 01:17:54, null, 1, admin 1 (a@a.a), Request delete approved by a@a.a, 2025-08-27 02:25:06, f, null). (Connection: client, SQL: update \"domain_cancellation_requests\" set \"support_agent_id\" = 1, \"support_agent_name\" = admin 1 (a@a.a), \"feedback_date\" = 2025-08-27 02:25:06, \"support_note\" = Request delete approved by a@a.a, \"is_refunded\" = 0, \"deleted_at\" = ? where \"domain_id\" = 130)","code":"23502"}  
[2025-08-27 02:28:14] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-27 02:28:32] local.ERROR: {"query":[],"parameter":{"domainName":"palwataw.net","userEmail":"<EMAIL>","domainId":130,"createdDate":"2025-08-27 01:16:37","userID":7,"email":"<EMAIL>"},"error":"Illuminate\\Database\\QueryException","message":"SQLSTATE[42703]: Undefined column: 7 ERROR:  column \"updated_at\" of relation \"domain_cancellation_requests\" does not exist
LINE 1: ...e\" = $3, \"support_note\" = $4, \"is_refunded\" = $5, \"updated_a...
                                                             ^ (Connection: client, SQL: update \"domain_cancellation_requests\" set \"support_agent_id\" = 1, \"support_agent_name\" = admin 1 (a@a.a), \"feedback_date\" = 2025-08-27 02:28:32, \"support_note\" = Request delete approved by a@a.a, \"is_refunded\" = 0, \"updated_at\" = 2025-08-27 02:28:32 where \"domain_id\" = 130)","code":"42703"}  
[2025-08-27 02:32:51] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-27 02:33:11] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-27 02:33:39] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-27 02:34:01] local.INFO: ApprovalDeleteRequest: Running...  
[2025-08-27 02:34:01] local.INFO: ApprovalDeleteRequest: Processed 0 expired requests  
[2025-08-27 02:34:01] local.INFO: ApprovalDeleteRequest: Done  
[2025-08-27 02:35:01] local.INFO: ApprovalDeleteRequest: Running...  
[2025-08-27 02:35:01] local.INFO: ApprovalDeleteRequest: Processed 5 expired requests  
[2025-08-27 02:35:01] local.INFO: ApprovalDeleteRequest: Done  
[2025-08-27 02:35:32] local.ERROR: Guest User cURL error 28: Operation timed out after 30005 milliseconds with 0 bytes received (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://130.211.227.70:8001/verisign/v3_1/domain/info  
[2025-08-27 02:35:32] local.ERROR: Error Unknown  
[2025-08-27 02:35:32] local.INFO: number of attempts: 1  
[2025-08-27 02:35:32] local.ERROR: Error Unknown  
[2025-08-27 02:35:32] local.ERROR: {"query":[],"parameter":[],"error":"App\\Exceptions\\FailedRequestException","message":"Error Unknown","code":520}  
[2025-08-27 02:35:35] local.INFO: Domain limittestme.net has clientDeleteProhibited status, cannot delete  
[2025-08-27 02:35:35] local.ERROR: Domain limittestme.net has clientDeleteProhibited status  
[2025-08-27 02:35:35] local.INFO: number of attempts: 1  
[2025-08-27 02:35:35] local.ERROR: Domain limittestme.net has clientDeleteProhibited status  
[2025-08-27 02:35:35] local.ERROR: {"query":[],"parameter":[],"error":"Exception","message":"Domain limittestme.net has clientDeleteProhibited status","code":0}  
[2025-08-27 02:36:19] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
