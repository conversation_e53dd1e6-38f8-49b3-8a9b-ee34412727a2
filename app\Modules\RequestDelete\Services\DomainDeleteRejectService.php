<?php

namespace App\Modules\RequestDelete\Services;

use App\Modules\Client\Constants\DomainStatus;
use App\Util\Constant\UserDomainStatus;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class DomainDeleteRejectService
{
    private Carbon $now;

    public function __construct()
    {
        $this->now = Carbon::now();
    }

    public static function instance()
    {
        return new self;
    }

    public function rejectDeleteRequestSave($request)
    {
        $data = $request->all();

        $domainInfo = DB::client()->table('domain_cancellation_requests')
            ->join('domains', 'domain_cancellation_requests.domain_id', '=', 'domains.id')
            ->join('users', 'domain_cancellation_requests.user_id', '=', 'users.id')
            ->where('domain_cancellation_requests.domain_id', $data['domainId'])
            ->select([
                'domains.name as domainName',
                'domain_cancellation_requests.user_id as userID',
                'users.email as userEmail',
                'domain_cancellation_requests.reason'
            ])
            ->first();

        if (!$domainInfo) {
            throw new \Exception("Domain deletion request not found for domain ID: {$data['domainId']}");
        }

        $data['domainName'] = $domainInfo->domainName;
        $data['userID'] = $domainInfo->userID;
        $data['userEmail'] = $domainInfo->userEmail;
        $data['reason'] = $domainInfo->reason ?? 'Domain deletion request';

        $this->processRejectRequest($data);
    }

    private function processRejectRequest(array $data): void
    {
        $adminId = Auth::id();
        $adminName = Auth::user()->name ?? 'System';
        $adminEmail = Auth::user()->email ?? '<EMAIL>';
        $supportNote = $data['support_note'] ?? "Request delete rejected by {$adminEmail}";

        $this->rejectDomainDeletionRequest($data, $adminId, $adminName, $supportNote);
    }

    private function rejectDomainDeletionRequest(array $data, int $adminId, string $adminName, string $supportNote): void
    {
        // Update the domain cancellation request
        DB::client()->table('domain_cancellation_requests')
            ->where('domain_id', $data['domainId'])
            ->update([
                'support_agent_id' => $adminId,
                'support_agent_name' => $adminName . ' (' . Auth::user()->email . ')',
                'support_note' => $supportNote,
                'feedback_date' => now(),
            ]);

        $this->reactivateDomain($data['domainId']);

        $this->sendRejectionNotification($data, $adminName);

        $this->logDomainRejectionHistory($data, $adminName);
    }

    private function reactivateDomain(int $domainId): void
    {
        $timestamp = now();

        DB::client()->table('domains')
            ->where('id', $domainId)
            ->update([
                'status' => DomainStatus::ACTIVE,
                'updated_at' => $timestamp,
            ]);

        DB::client()->table('registered_domains')
            ->where('domain_id', $domainId)
            ->update([
                'status' => UserDomainStatus::OWNED,
                'updated_at' => $timestamp,
            ]);
    }

    private function sendRejectionNotification(array $data, string $adminName): void
    {
        $message = 'Your request to delete the domain "' . $data['domainName'] . '" has been rejected by ' . $adminName . '. The domain has been reactivated and is now available for use.';

        DB::client()->table('notifications')->insert([
            'user_id' => $data['userID'],
            'title' => 'Domain Deletion Request Rejected',
            'redirect_url' => '/domain',
            'message' => $message,
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }

    private function logDomainRejectionHistory(array $data, string $adminName): void
    {
        $adminEmail = Auth::user()->email ?? '<EMAIL>';
        $message = 'Domain "' . $data['domainName'] . '" deletion request rejected and domain reactivated by ' . $adminName . ' (' . $adminEmail . ')';

        DB::client()->table('domain_transaction_histories')->insert([
            'domain_id' => $data['domainId'],
            'type' => 'DOMAIN_DELETE_REJECTED',
            'user_id' => $data['userID'],
            'status' => 'success',
            'message' => $message,
            'payload' => json_encode($data),
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }
}
